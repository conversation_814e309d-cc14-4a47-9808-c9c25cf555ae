#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
测试重构后的用户数据结构模块

验证所有响应类是否正确继承自base_modes.py中的基类，
并且能够正常创建和序列化。
"""

import uuid
from datetime import datetime
from apps.schemas.users import (
    UserResponse,
    UserListResponse,
    UserLoginResponse,
    UserDetailResponse,
    UserCreateResponse,
    UserUpdateResponse,
    UserDeleteResponse,
    UserLoginData
)


def test_user_response():
    """测试用户响应数据结构"""
    print("=== 测试 UserResponse ===")
    
    user = UserResponse(
        id=uuid.uuid4(),
        username="test_user",
        email="<EMAIL>",
        comment="测试用户",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    print(f"用户ID: {user.id}")
    print(f"用户名: {user.username}")
    print(f"邮箱: {user.email}")
    print(f"备注: {user.comment}")
    print()


def test_user_list_response():
    """测试用户列表响应数据结构"""
    print("=== 测试 UserListResponse ===")
    
    # 创建测试用户列表
    users = [
        UserResponse(
            id=uuid.uuid4(),
            username=f"user_{i}",
            email=f"user{i}@example.com",
            comment=f"测试用户{i}",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        for i in range(1, 4)
    ]
    
    # 使用类方法创建响应
    response = UserListResponse.from_users(
        users=users,
        total=100,
        page=1,
        page_size=10
    )
    
    print(f"响应码: {response.code}")
    print(f"响应消息: {response.message}")
    print(f"用户数量: {len(response.data)}")
    print(f"总数: {response.total}")
    print(f"当前页: {response.page}")
    print(f"每页大小: {response.page_size}")
    print()


def test_user_login_response():
    """测试用户登录响应数据结构"""
    print("=== 测试 UserLoginResponse ===")
    
    # 创建测试用户
    user = UserResponse(
        id=uuid.uuid4(),
        username="login_user",
        email="<EMAIL>",
        comment="登录测试用户",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    # 使用类方法创建登录响应
    response = UserLoginResponse.from_login_data(
        access_token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_token",
        refresh_token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.refresh_token",
        expires_in=3600,
        user=user
    )
    
    print(f"响应码: {response.code}")
    print(f"响应消息: {response.message}")
    print(f"访问令牌: {response.data.access_token[:50]}...")
    print(f"刷新令牌: {response.data.refresh_token[:50]}...")
    print(f"令牌类型: {response.data.token_type}")
    print(f"过期时间: {response.data.expires_in}秒")
    print(f"用户名: {response.data.user.username}")
    print()


def test_user_detail_response():
    """测试用户详情响应数据结构"""
    print("=== 测试 UserDetailResponse ===")
    
    user = UserResponse(
        id=uuid.uuid4(),
        username="detail_user",
        email="<EMAIL>",
        comment="详情测试用户",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    response = UserDetailResponse.from_user(user)
    
    print(f"响应码: {response.code}")
    print(f"响应消息: {response.message}")
    print(f"用户ID: {response.data.id}")
    print(f"用户名: {response.data.username}")
    print()


def test_user_create_response():
    """测试用户创建响应数据结构"""
    print("=== 测试 UserCreateResponse ===")
    
    user = UserResponse(
        id=uuid.uuid4(),
        username="new_user",
        email="<EMAIL>",
        comment="新创建的用户",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    response = UserCreateResponse.from_user(user)
    
    print(f"响应码: {response.code}")
    print(f"响应消息: {response.message}")
    print(f"创建的用户ID: {response.data.id}")
    print(f"创建的用户名: {response.data.username}")
    print()


def test_user_update_response():
    """测试用户更新响应数据结构"""
    print("=== 测试 UserUpdateResponse ===")
    
    user = UserResponse(
        id=uuid.uuid4(),
        username="updated_user",
        email="<EMAIL>",
        comment="更新后的用户",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    response = UserUpdateResponse.from_user(user)
    
    print(f"响应码: {response.code}")
    print(f"响应消息: {response.message}")
    print(f"更新的用户ID: {response.data.id}")
    print(f"更新的用户名: {response.data.username}")
    print()


def test_user_delete_response():
    """测试用户删除响应数据结构"""
    print("=== 测试 UserDeleteResponse ===")
    
    response = UserDeleteResponse.success()
    
    print(f"响应码: {response.code}")
    print(f"响应消息: {response.message}")
    print(f"响应数据: {response.data}")
    print()


def main():
    """主测试函数"""
    print("开始测试重构后的用户数据结构...")
    print("=" * 50)
    
    try:
        test_user_response()
        test_user_list_response()
        test_user_login_response()
        test_user_detail_response()
        test_user_create_response()
        test_user_update_response()
        test_user_delete_response()
        
        print("=" * 50)
        print("✅ 所有测试通过！重构成功！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
