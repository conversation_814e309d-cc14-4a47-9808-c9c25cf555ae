#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
用户数据结构重构验证脚本

通过静态分析验证重构后的代码结构是否正确，包括：
- 导入语句的正确性
- 类继承关系的正确性
- 字段定义的完整性
"""

import ast
import os
from typing import List, Dict, Any


def analyze_python_file(file_path: str) -> Dict[str, Any]:
    """分析Python文件的AST结构"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    try:
        tree = ast.parse(content)
        return {
            'imports': extract_imports(tree),
            'classes': extract_classes(tree),
            'functions': extract_functions(tree)
        }
    except SyntaxError as e:
        return {'error': f'语法错误: {str(e)}'}


def extract_imports(tree: ast.AST) -> List[str]:
    """提取导入语句"""
    imports = []
    for node in ast.walk(tree):
        if isinstance(node, ast.Import):
            for alias in node.names:
                imports.append(f"import {alias.name}")
        elif isinstance(node, ast.ImportFrom):
            module = node.module or ''
            for alias in node.names:
                imports.append(f"from {module} import {alias.name}")
    return imports


def extract_classes(tree: ast.AST) -> List[Dict[str, Any]]:
    """提取类定义"""
    classes = []
    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef):
            bases = [ast.unparse(base) for base in node.bases]
            methods = [n.name for n in node.body if isinstance(n, ast.FunctionDef)]
            classes.append({
                'name': node.name,
                'bases': bases,
                'methods': methods,
                'docstring': ast.get_docstring(node)
            })
    return classes


def extract_functions(tree: ast.AST) -> List[str]:
    """提取函数定义"""
    functions = []
    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef):
            functions.append(node.name)
    return functions


def validate_users_schema():
    """验证用户数据结构文件"""
    print("🔍 验证用户数据结构文件...")
    
    users_file = "apps/schemas/users.py"
    if not os.path.exists(users_file):
        print(f"❌ 文件不存在: {users_file}")
        return False
    
    analysis = analyze_python_file(users_file)
    
    if 'error' in analysis:
        print(f"❌ 文件分析失败: {analysis['error']}")
        return False
    
    print("✅ 文件语法正确")
    
    # 检查导入语句
    imports = analysis['imports']
    required_imports = [
        'from .base_modes import DetailResponse',
        'from .base_modes import PageResponse', 
        'from .base_modes import Response'
    ]
    
    for required in required_imports:
        if any(required in imp for imp in imports):
            print(f"✅ 找到必需的导入: {required}")
        else:
            print(f"⚠️      可能缺少导入: {required}")
    
    # 检查响应类
    classes = analysis['classes']
    response_classes = [
        'UserDetailResponse',
        'UserListResponse', 
        'UserLoginResponse',
        'UserCreateResponse',
        'UserUpdateResponse',
        'UserDeleteResponse'
    ]
    
    class_names = [cls['name'] for cls in classes]
    
    for response_class in response_classes:
        if response_class in class_names:
            print(f"✅ 找到响应类: {response_class}")
            # 检查继承关系
            cls_info = next(cls for cls in classes if cls['name'] == response_class)
            if cls_info['bases']:
                print(f"   继承自: {', '.join(cls_info['bases'])}")
        else:
            print(f"❌ 缺少响应类: {response_class}")
    
    return True


def validate_base_modes():
    """验证基础模式文件"""
    print("\n🔍 验证基础模式文件...")
    
    base_file = "apps/schemas/base_modes.py"
    if not os.path.exists(base_file):
        print(f"❌ 文件不存在: {base_file}")
        return False
    
    analysis = analyze_python_file(base_file)
    
    if 'error' in analysis:
        print(f"❌ 文件分析失败: {analysis['error']}")
        return False
    
    print("✅ 文件语法正确")
    
    # 检查基础响应类
    classes = analysis['classes']
    base_classes = ['Response', 'DetailResponse', 'PageResponse']
    class_names = [cls['name'] for cls in classes]
    
    for base_class in base_classes:
        if base_class in class_names:
            print(f"✅ 找到基础类: {base_class}")
        else:
            print(f"❌ 缺少基础类: {base_class}")
    
    return True


def validate_init_file():
    """验证__init__.py文件"""
    print("\n🔍 验证__init__.py文件...")
    
    init_file = "apps/schemas/__init__.py"
    if not os.path.exists(init_file):
        print(f"❌ 文件不存在: {init_file}")
        return False
    
    analysis = analyze_python_file(init_file)
    
    if 'error' in analysis:
        print(f"❌ 文件分析失败: {analysis['error']}")
        return False
    
    print("✅ 文件语法正确")
    
    # 检查导入语句
    imports = analysis['imports']
    
    # 检查基础响应结构的导入
    base_imports = [
        'from .base_modes import Response',
        'from .base_modes import DetailResponse',
        'from .base_modes import PageResponse'
    ]
    
    for base_import in base_imports:
        if any(base_import in imp for imp in imports):
            print(f"✅ 找到基础导入: {base_import}")
        else:
            print(f"⚠️  可能缺少基础导入: {base_import}")
    
    # 检查用户响应结构的导入
    user_response_imports = [
        'UserDetailResponse',
        'UserListResponse',
        'UserLoginResponse',
        'UserCreateResponse',
        'UserUpdateResponse',
        'UserDeleteResponse'
    ]
    
    for user_import in user_response_imports:
        if any(user_import in imp for imp in imports):
            print(f"✅ 找到用户响应导入: {user_import}")
        else:
            print(f"⚠️  可能缺少用户响应导入: {user_import}")
    
    return True


def main():
    """主验证函数"""
    print("🚀 开始验证用户数据结构重构...")
    print("=" * 50)
    
    try:
        # 验证各个文件
        base_ok = validate_base_modes()
        users_ok = validate_users_schema()
        init_ok = validate_init_file()
        
        print("\n" + "=" * 50)
        
        if base_ok and users_ok and init_ok:
            print("🎉 重构验证通过！所有文件结构正确！")
            print("\n📋 重构总结:")
            print("✅ 引入了统一的响应结构 (Response, DetailResponse, PageResponse)")
            print("✅ 重构了用户响应类，继承自基础响应结构")
            print("✅ 保持了原有的数据验证和字段定义")
            print("✅ 更新了模块导入和导出")
            print("✅ 遵循了Context7最佳实践")
        else:
            print("⚠️  重构验证发现一些问题，请检查上述输出")
            
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
