#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 16:33
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : __init__.py
# @Update  : 2025/8/7 16:30 数据结构模块初始化

"""
数据结构模块

该模块包含所有Pydantic数据结构的定义，用于：
- API请求和响应的数据验证
- 数据序列化和反序列化
- JSON Schema生成
- 类型提示和IDE支持

所有数据结构都基于Pydantic 2.0，提供：
- 完整的类型注解支持
- 自动数据验证和转换
- 详细的错误信息
- 高性能的序列化
"""

# 导入基础响应结构
from .base_modes import (
    Response,
    DetailResponse,
    PageResponse,
)

# 导入用户相关的数据结构
from .users import (
    UserBase,
    UserCreate,
    UserUpdate,
    UserResponse,
    UserQuery,
    UserLogin,
    UserLoginData,
    # 统一响应结构
    UserDetailResponse,
    UserListResponse,
    UserLoginResponse,
    UserCreateResponse,
    UserUpdateResponse,
    UserDeleteResponse,
)

# 导出所有数据结构，便于其他模块导入
__all__ = [
    # 基础响应结构
    "Response",
    "DetailResponse",
    "PageResponse",

    # 用户相关数据结构
    "UserBase",
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserQuery",
    "UserLogin",
    "UserLoginData",

    # 用户响应结构
    "UserDetailResponse",
    "UserListResponse",
    "UserLoginResponse",
    "UserCreateResponse",
    "UserUpdateResponse",
    "UserDeleteResponse",

    # 在这里添加其他模块的数据结构
    # "PostCreate",
    # "PostResponse",
    # "CommentCreate",
    # "CommentResponse",
]
