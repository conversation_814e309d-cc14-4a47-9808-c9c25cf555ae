#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/8 09:55
# <AUTHOR> la<PERSON><PERSON>
# @Email   : <EMAIL>
# @File    : base_modes.py
# @Update  : 2025/8/8 09:55 更新描述
from typing import Any, Dict, List

from pydantic import BaseModel


class Response(BaseModel):
    """基础响应数据结构"""
    code: int = 1000
    message: str = "success"
    data: Any = None


class DetailResponse(Response):
    """详情响应数据结构"""
    data: Dict[str, Any] = None


class PageResponse(Response):
    """分页响应数据结构"""
    data: List[Any] = []
    total: int = 0
    page: int = 1
    page_size: int = 10
