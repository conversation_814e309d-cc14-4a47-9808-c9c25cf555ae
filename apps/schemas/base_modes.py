#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/8 09:55
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : base_modes.py
# @Update  : 2025/8/8 09:55 更新描述
from typing import Any, Dict, List

from pydantic import BaseModel


class Response(BaseModel):
    """基础响应数据结构"""
    code: int = 1000
    message: str = "success"
    data: Any = None


class DetailResponse(Response):
    """详情响应数据结构"""
    data: Dict[str, Any] = None

    @classmethod
    def from_data(cls, data: Dict[str, Any]) -> "DetailResponse":
        """
        从数据字典创建详情响应
        :param data:
        :return:
        """
        return cls(data=data)


class PageResponse(Response):
    """分页响应数据结构"""
    data: List[Any] = []
    total: int = 0
    page: int = 1
    page_size: int = 10

    @classmethod
    def from_data(cls, data: List[Any], total: int, page: int, page_size: int) -> "PageResponse":
        """
        从数据列表创建分页响应
        :param data:
        :param total:
        :param page:
        :param page_size:
        :return:
        """
        return cls(data=data, total=total, page=page, page_size=page_size)
