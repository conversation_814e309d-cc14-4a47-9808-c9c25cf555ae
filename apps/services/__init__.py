#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 16:34
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : __init__.py
# @Update  : 2025/8/7 17:00 服务层模块初始化

"""
服务层模块

该模块包含所有业务逻辑服务的定义，提供：
- 业务逻辑处理和数据验证
- 数据库操作的封装和抽象
- 事务管理和错误处理
- 缓存和性能优化

服务层遵循以下设计原则：
- 单一职责：每个服务类负责特定领域的业务逻辑
- 依赖注入：通过构造函数注入依赖项
- 异常处理：统一的异常处理和错误信息
- 类型安全：完整的类型注解支持
- 可测试性：便于单元测试和集成测试

架构层次：
Controller Layer -> Service Layer -> Repository/Model Layer
"""

# 导入基础服务类和异常
from .base import (
    ServiceError,
    NotFoundError,
    ConflictError,
    ValidationError,
)

# 导入具体的服务类
from .users import UserService, user_service
from .auth import AuthService, PasswordManager, JWTManager, auth_service

# 导出所有服务类和异常，便于其他模块导入
__all__ = [
    # 基础服务类和异常
    "ServiceError",
    "NotFoundError",
    "ConflictError",
    "ValidationError",

    # 用户服务
    "UserService",
    "user_service",  # 预配置的用户服务实例

    # 认证服务
    "AuthService",
    "PasswordManager",
    "JWTManager",
    "auth_service",  # 预配置的认证服务实例

    # 在这里添加其他服务
    # "PostService",
    # "post_service",
    # "CommentService",
    # "comment_service",
]
