#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 17:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : base.py
# @Update  : 2025/8/7 17:00 优化的基础服务类实现

"""
基础服务模块 - 优化版本

该模块定义了服务层的基础类和通用功能，遵循Context7最佳实践：
- 现代化的异步CRUD操作模式
- 统一的错误处理和异常管理
- 类型安全的泛型设计
- 高性能的数据库操作
- 完整的事务管理支持

设计原则：
- 单一职责：每个服务类负责特定领域的业务逻辑
- 依赖注入：通过构造函数注入依赖项
- 异常处理：统一的异常处理和错误信息
- 类型安全：完整的类型注解支持
- 可测试性：便于单元测试和集成测试

参考：SQLAlchemy CRUD Plus 最佳实践
"""

from __future__ import annotations

import logging
from typing import Any, Dict, List, Optional

from models import BaseModel

# 配置日志
logger = logging.getLogger(__name__)


class ServiceError(Exception):
    """服务层基础异常类"""

    def __init__(
        self,
        message: str,
        code: str = "SERVICE_ERROR",
        details: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        self.message = message
        self.code = code
        self.details = details or {}
        self.original_error = original_error
        super().__init__(self.message)


class NotFoundError(ServiceError):
    """资源未找到异常"""

    def __init__(self, resource: str, identifier: Any):
        message = f"{resource} with identifier '{identifier}' not found"
        super().__init__(
            message,
            "NOT_FOUND",
            {"resource": resource, "identifier": str(identifier)}
        )


class ConflictError(ServiceError):
    """资源冲突异常"""

    def __init__(self, message: str, field: Optional[str] = None, value: Optional[Any] = None):
        details = {}
        if field:
            details["field"] = field
        if value:
            details["value"] = str(value)
        super().__init__(message, "CONFLICT", details)


class ValidationError(ServiceError):
    """数据验证异常"""

    def __init__(self, message: str, field: Optional[str] = None, value: Optional[Any] = None):
        details = {}
        if field:
            details["field"] = field
        if value:
            details["value"] = str(value)
        super().__init__(message, "VALIDATION_ERROR", details)

