#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 17:44
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : __init__.py
# @Update  : 2025/8/7 18:00 API模块初始化

"""
API模块

该模块包含所有FastAPI路由和端点的定义，遵循Context7最佳实践：
- RESTful API设计
- 模块化路由组织
- 统一的错误处理
- 完整的API文档
- 安全的认证机制

模块结构：
- auth.py: 认证相关API端点
- users.py: 用户管理API端点
- dependencies.py: 共享依赖项
- router.py: 主路由器配置

设计原则：
- 单一职责：每个模块负责特定的API功能
- 依赖注入：使用FastAPI的依赖注入系统
- 类型安全：完整的类型注解支持
- 文档完整：详细的API文档和示例
- 安全优先：完善的认证和授权机制

架构层次：
FastAPI App -> Router -> Dependencies -> Services -> Models
"""

# 导入主路由器
from .router import main_router, api_v1_router

# 导入子路由器

# 导入依赖项
from . import dependencies

# 导出所有公共接口
__all__ = [
    # 主路由器
    "main_router",
    "api_v1_router",

    # 子路由模块

    # 依赖项模块
    "dependencies",
]
