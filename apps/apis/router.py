#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 18:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : router.py
# @Update  : 2025/8/7 18:00 主API路由器

"""
主API路由器模块

该模块负责组织和配置所有API路由，遵循Context7和FastAPI最佳实践：
- 模块化路由组织
- 统一的API前缀和版本管理
- 全局中间件和异常处理
- API文档配置
- CORS和安全配置

路由结构：
- /api/v1/auth/* - 认证相关API
- /api/v1/users/* - 用户管理API
- /api/v1/health - 健康检查API

参考：FastAPI最佳实践和API设计规范
"""

from __future__ import annotations

from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from apis.dependencies import get_db_session

# 创建API版本1路由器
api_v1_router = APIRouter(
    prefix="/api/v1",
    responses={
        500: {"description": "内部服务器错误"},
        503: {"description": "服务不可用"},
    },
)

# 创建主API路由器
main_router = APIRouter()


# 健康检查端点
@api_v1_router.get(
    "/health",
    summary="健康检查",
    description="检查API服务的健康状态",
    responses={
        200: {
            "description": "服务正常",
            "content":     {
                "application/json": {
                    "example": {
                        "status":    "healthy",
                        "version":   "1.0.0",
                        "timestamp": "2024-01-01T12:00:00Z",
                        "database":  "connected"
                    }
                }
            }
        },
        503: {
            "description": "服务不可用",
            "content":     {
                "application/json": {
                    "example": {
                        "status":    "unhealthy",
                        "version":   "1.0.0",
                        "timestamp": "2024-01-01T12:00:00Z",
                        "database":  "disconnected",
                        "error":     "数据库连接失败"
                    }
                }
            }
        }
    },
    tags=["系统监控"]
)
async def health_check(
    session: AsyncSession = Depends(get_db_session)
) -> JSONResponse:
    """
    健康检查
    
    检查API服务和相关组件的健康状态。
    
    **检查项目**：
    - API服务状态
    - 数据库连接状态
    - 系统时间戳
    - 服务版本信息
    
    **返回状态**：
    - **200 OK**: 所有组件正常
    - **503 Service Unavailable**: 存在组件异常
    """
    from datetime import datetime, timezone
    import sys

    health_data = {
        "status":         "healthy",
        "version":        "1.0.0",
        "timestamp":      datetime.now(timezone.utc).isoformat(),
        "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        "database":       "connected"
    }

    try:
        # 测试数据库连接
        await session.execute(text("SELECT 1"))
        health_data["database"] = "connected"

    except Exception as e:
        health_data["status"] = "unhealthy"
        health_data["database"] = "disconnected"
        health_data["error"] = f"数据库连接失败: {str(e)}"

        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content=health_data
        )

    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=health_data
    )


# 根路径端点
@main_router.get(
    "/",
    summary="API根路径",
    description="API服务的根路径，返回基本信息",
    responses={
        200: {
            "description": "API基本信息",
            "content":     {
                "application/json": {
                    "example": {
                        "name":        "EchoNote API",
                        "version":     "1.0.0",
                        "description": "EchoNote项目的RESTful API服务",
                        "docs_url":    "/docs",
                        "redoc_url":   "/redoc",
                        "health_url":  "/api/v1/health"
                    }
                }
            }
        }
    },
    tags=["系统信息"]
)
async def root() -> dict:
    """
    API根路径
    
    返回API服务的基本信息和导航链接。
    
    **返回信息**：
    - 服务名称和版本
    - 服务描述
    - 文档链接
    - 健康检查链接
    """
    return {
        "name":        "EchoNote API",
        "version":     "1.0.0",
        "description": "EchoNote项目的RESTful API服务",
        "docs_url":    "/docs",
        "redoc_url":   "/redoc",
        "health_url":  "/api/v1/health",
        "api_prefix":  "/api/v1"
    }


# API信息端点
@api_v1_router.get(
    "/info",
    summary="API信息",
    description="获取API的详细信息和统计数据",
    responses={
        200: {
            "description": "API详细信息",
            "content":     {
                "application/json": {
                    "example": {
                        "api_version":         "1.0.0",
                        "endpoints_count":     15,
                        "available_endpoints": [
                            "/api/v1/auth/login",
                            "/api/v1/auth/refresh",
                            "/api/v1/users/",
                            "/api/v1/users/me"
                        ],
                        "authentication":      "JWT Bearer Token",
                        "rate_limiting":       "100 requests per minute",
                        "documentation":       {
                            "swagger_ui":   "/docs",
                            "redoc":        "/redoc",
                            "openapi_json": "/openapi.json"
                        }
                    }
                }
            }
        }
    },
    tags=["系统信息"]
)
async def api_info() -> dict:
    """
    API信息
    
    获取API的详细信息，包括版本、端点统计、认证方式等。
    
    **返回信息**：
    - API版本信息
    - 可用端点列表
    - 认证方式说明
    - 限流策略
    - 文档链接
    """
    return {
        "api_version":         "1.0.0",
        "framework":           "FastAPI",
        "python_version":      "3.11+",
        "database":            "PostgreSQL",
        "authentication":      "JWT Bearer Token",
        "available_endpoints": {
            "auth":   [
                "POST /api/v1/auth/login",
                "POST /api/v1/auth/token",
                "POST /api/v1/auth/refresh",
                "POST /api/v1/auth/logout",
                "GET /api/v1/auth/me",
                "POST /api/v1/auth/check-password-strength"
            ],
            "users":  [
                "POST /api/v1/users/",
                "GET /api/v1/users/",
                "GET /api/v1/users/me",
                "GET /api/v1/users/{user_id}",
                "PUT /api/v1/users/{user_id}",
                "DELETE /api/v1/users/{user_id}",
                "POST /api/v1/users/{user_id}/change-password",
                "GET /api/v1/users/search",
                "GET /api/v1/users/statistics"
            ],
            "system": [
                "GET /api/v1/health",
                "GET /api/v1/info"
            ]
        },
        "features":            [
            "JWT认证",
            "密码安全哈希",
            "用户权限管理",
            "数据验证",
            "API文档",
            "健康检查",
            "错误处理"
        ],
        "documentation":       {
            "swagger_ui":   "/docs",
            "redoc":        "/redoc",
            "openapi_json": "/openapi.json"
        },
        "security":            {
            "authentication":   "JWT Bearer Token",
            "password_hashing": "bcrypt",
            "token_expiry":     "30 minutes (access), 7 days (refresh)",
            "cors_enabled":     True
        }
    }


# 包含子路由

# 将v1路由包含到主路由
main_router.include_router(api_v1_router)

# 导出路由器
__all__ = ["main_router", "api_v1_router"]
