#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
用户数据结构重构测试文件

测试重构后的用户数据结构是否正常工作，包括：
- 基础数据结构的创建和验证
- 统一响应结构的使用
- 类型注解和验证功能
"""

import uuid
from datetime import datetime
from typing import Dict, Any

# 导入重构后的数据结构
from apps.schemas import (
    # 基础响应结构
    Response,
    DetailResponse,
    PageResponse,
    
    # 用户数据结构
    UserBase,
    UserCreate,
    UserUpdate,
    UserResponse,
    UserQuery,
    UserLogin,
    UserLoginData,
    
    # 用户响应结构
    UserDetailResponse,
    UserListResponse,
    UserLoginResponse,
    UserCreateResponse,
    UserUpdateResponse,
    UserDeleteResponse,
)


def test_basic_response_structures():
    """测试基础响应结构"""
    print("🧪 测试基础响应结构...")
    
    # 测试基础响应
    basic_response = Response()
    print(f"✅ 基础响应: {basic_response.model_dump()}")
    
    # 测试详情响应
    detail_response = DetailResponse(data={"test": "data"})
    print(f"✅ 详情响应: {detail_response.model_dump()}")
    
    # 测试分页响应
    page_response = PageResponse(data=[{"item": 1}], total=1)
    print(f"✅ 分页响应: {page_response.model_dump()}")


def test_user_data_structures():
    """测试用户数据结构"""
    print("\n🧪 测试用户数据结构...")
    
    # 测试用户创建
    user_create = UserCreate(
        username="test_user",
        email="<EMAIL>",
        comment="测试用户",
        password="TestPass123!",
        confirm_password="TestPass123!"
    )
    print(f"✅ 用户创建: {user_create.model_dump(exclude={'password', 'confirm_password'})}")
    
    # 测试用户更新
    user_update = UserUpdate(
        username="updated_user",
        email="<EMAIL>"
    )
    print(f"✅ 用户更新: {user_update.model_dump(exclude_none=True)}")
    
    # 测试用户响应
    user_response = UserResponse(
        id=uuid.uuid4(),
        username="test_user",
        email="<EMAIL>",
        comment="测试用户",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    print(f"✅ 用户响应: {user_response.model_dump()}")


def test_user_response_structures():
    """测试用户响应结构"""
    print("\n🧪 测试用户响应结构...")
    
    # 创建示例用户数据
    user_data = UserResponse(
        id=uuid.uuid4(),
        username="test_user",
        email="<EMAIL>",
        comment="测试用户",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    # 测试用户详情响应
    user_detail_response = UserDetailResponse(
        code=1000,
        message="获取用户信息成功",
        data=user_data
    )
    print(f"✅ 用户详情响应: {user_detail_response.model_dump()}")
    
    # 测试用户列表响应
    user_list_response = UserListResponse(
        code=1000,
        message="获取用户列表成功",
        data=[user_data],
        total=1,
        page=1,
        page_size=10
    )
    print(f"✅ 用户列表响应: {user_list_response.model_dump()}")
    
    # 测试用户登录响应
    login_data = UserLoginData(
        access_token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        refresh_token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        token_type="bearer",
        expires_in=3600,
        user=user_data
    )
    
    user_login_response = UserLoginResponse(
        code=1000,
        message="登录成功",
        data=login_data
    )
    print(f"✅ 用户登录响应: {user_login_response.model_dump()}")
    
    # 测试用户删除响应
    user_delete_response = UserDeleteResponse(
        code=1000,
        message="用户删除成功",
        data=None
    )
    print(f"✅ 用户删除响应: {user_delete_response.model_dump()}")


def test_validation():
    """测试数据验证功能"""
    print("\n🧪 测试数据验证功能...")
    
    try:
        # 测试用户名验证
        invalid_user = UserCreate(
            username="123invalid",  # 以数字开头，应该失败
            email="<EMAIL>",
            password="TestPass123!",
            confirm_password="TestPass123!"
        )
    except Exception as e:
        print(f"✅ 用户名验证失败（预期）: {str(e)}")
    
    try:
        # 测试密码确认验证
        invalid_password = UserCreate(
            username="test_user",
            email="<EMAIL>",
            password="TestPass123!",
            confirm_password="DifferentPass123!"  # 密码不匹配，应该失败
        )
    except Exception as e:
        print(f"✅ 密码确认验证失败（预期）: {str(e)}")


def main():
    """主测试函数"""
    print("🚀 开始测试用户数据结构重构...")
    
    try:
        test_basic_response_structures()
        test_user_data_structures()
        test_user_response_structures()
        test_validation()
        
        print("\n🎉 所有测试通过！重构成功！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
